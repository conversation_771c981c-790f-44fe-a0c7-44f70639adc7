"""
AP3X Crypto Agent Backend - Main FastAPI Application
Provides REST API and WebSocket gateway for the LangGraph agent
"""

import asyncio
import json
import logging
import os
import uuid
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional

import structlog
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from services import AgentService
from core import Settings, get_settings
from .endpoints import router as api_router
from .websocket import ConnectionManager, StreamingHandler, WorkspaceCommandHandler
from core.error_handling import (
    error_handler, health_checker, setup_error_monitoring,
    global_exception_handler, http_exception_handler, handle_exceptions
)

# Load environment variables
load_dotenv()

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Global agent service instance
agent_service: Optional[AgentService] = None

# Global WebSocket managers
connection_manager = ConnectionManager()
streaming_handler: Optional[StreamingHandler] = None
workspace_command_handler: Optional[WorkspaceCommandHandler] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global agent_service, streaming_handler, workspace_command_handler
    
    logger.info("Starting AP3X Crypto Agent Backend")
    
    # Initialize agent service
    settings = get_settings()
    agent_service = AgentService(settings)
    await agent_service.initialize()
    
    # Initialize WebSocket handlers
    streaming_handler = StreamingHandler(connection_manager)
    workspace_command_handler = WorkspaceCommandHandler(connection_manager)
    await workspace_command_handler.start_command_processor()
    
    # Setup error monitoring
    await setup_error_monitoring()
    
    logger.info("Agent service, WebSocket handlers, and error monitoring initialized successfully")
    
    yield
    
    # Cleanup
    if workspace_command_handler:
        await workspace_command_handler.stop_command_processor()
    
    if agent_service:
        await agent_service.cleanup()
    
    logger.info("AP3X Crypto Agent Backend shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="AP3X Crypto Agent Backend",
    description="LangGraph-based crypto agent with real-time blockchain data and web research capabilities",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(api_router, prefix="/api/v1", tags=["Extended API"])

# Add exception handlers
app.add_exception_handler(Exception, global_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)


# Request/Response Models
class QueryRequest(BaseModel):
    """Request model for agent queries"""
    query: str = Field(..., description="User query for the agent")
    session_id: Optional[str] = Field(None, description="Optional session ID for conversation continuity")
    stream: bool = Field(False, description="Whether to stream the response")


class QueryResponse(BaseModel):
    """Response model for agent queries"""
    session_id: str
    final_answer: Optional[str] = None
    thinking_steps: List[str] = Field(default_factory=list)
    confidence_score: float = 0.0
    blockchain_data: Dict[str, Any] = Field(default_factory=dict)
    web_research_data: Dict[str, Any] = Field(default_factory=dict)
    status: str = "success"
    error: Optional[str] = None


class SessionInfo(BaseModel):
    """Session information model"""
    session_id: str
    created_at: str
    last_activity: str
    message_count: int
    status: str


class HealthResponse(BaseModel):
    """Health check response"""
    status: str
    version: str
    agent_status: str
    external_services: Dict[str, str]


# Dependency to get agent service
async def get_agent_service() -> AgentService:
    """Dependency to get the agent service"""
    if agent_service is None:
        raise HTTPException(status_code=503, detail="Agent service not initialized")
    return agent_service


# Health Check Endpoint
@app.get("/health", response_model=HealthResponse)
@handle_exceptions("Health check failed")
async def health_check(agent_svc: AgentService = Depends(get_agent_service)):
    """Health check endpoint"""
    # Check agent status
    agent_status = await agent_svc.get_status()
    
    # Check external services
    external_services = await agent_svc.check_external_services()
    
    # Run system health checks
    health_results = await health_checker.run_health_checks()
    
    # Determine overall status
    overall_status = "healthy"
    if agent_status != "healthy" or health_results["overall_status"] != "healthy":
        overall_status = "unhealthy"
    elif any(status != "healthy" for status in external_services.values()):
        overall_status = "degraded"
    
    return HealthResponse(
        status=overall_status,
        version="1.0.0",
        agent_status=agent_status,
        external_services=external_services
    )


# Agent Query Endpoint
@app.post("/query", response_model=QueryResponse)
@handle_exceptions("Failed to process query")
async def process_query(
    request: QueryRequest,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Process a query through the agent"""
    # Generate session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())
    
    logger.info("Processing query", session_id=session_id, query=request.query[:100])
    
    if request.stream:
        # For streaming requests, we'll use WebSocket
        return QueryResponse(
            session_id=session_id,
            status="streaming",
            final_answer="Use WebSocket endpoint for streaming responses"
        )
    
    # Process query through agent
    result = await agent_svc.process_query(request.query, session_id)
    
    return QueryResponse(
        session_id=session_id,
        final_answer=result.get("final_answer"),
        thinking_steps=result.get("thinking_steps", []),
        confidence_score=result.get("confidence_score", 0.0),
        blockchain_data=result.get("blockchain_data", {}),
        web_research_data=result.get("web_research_data", {}),
        status=result.get("status", "success"),
        error=result.get("error")
    )


# Session Management Endpoints
@app.get("/sessions/{session_id}", response_model=SessionInfo)
async def get_session(
    session_id: str,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Get session information"""
    try:
        session_info = await agent_svc.get_session_info(session_id)
        if not session_info:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return SessionInfo(**session_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get session info", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Delete a session"""
    try:
        await agent_svc.delete_session(session_id)
        return {"message": "Session deleted successfully"}
        
    except Exception as e:
        logger.error("Failed to delete session", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# WebSocket Endpoint for Real-time Communication
@app.websocket("/ws/{session_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    session_id: str,
    agent_svc: AgentService = Depends(get_agent_service)
):
    """Enhanced WebSocket endpoint for real-time agent interaction"""
    try:
        await connection_manager.connect(websocket, session_id, {
            "user_agent": websocket.headers.get("user-agent"),
            "client_ip": websocket.client.host if websocket.client else None
        })
    except Exception as e:
        logger.error("WebSocket connection failed", session_id=session_id, error=str(e))
        await websocket.close(code=1000)
        return
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
            except json.JSONDecodeError:
                await connection_manager.send_message(session_id, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
                continue
            
            message_type = message.get("type", "query")
            
            if message_type == "query":
                await handle_query_message(session_id, message, agent_svc)
            
            elif message_type == "subscribe_workspace":
                connection_manager.subscribe_to_workspace(session_id)
                await connection_manager.send_message(session_id, {
                    "type": "workspace_subscribed"
                })
            
            elif message_type == "unsubscribe_workspace":
                connection_manager.unsubscribe_from_workspace(session_id)
                await connection_manager.send_message(session_id, {
                    "type": "workspace_unsubscribed"
                })
            
            elif message_type == "ping":
                await connection_manager.send_message(session_id, {
                    "type": "pong",
                    "timestamp": "2024-01-01T00:00:00Z"
                })
            
            else:
                await connection_manager.send_message(session_id, {
                    "type": "error",
                    "message": f"Unknown message type: {message_type}"
                })
            
    except WebSocketDisconnect:
        connection_manager.disconnect(session_id)
        logger.info("WebSocket disconnected", session_id=session_id)
    except Exception as e:
        logger.error("WebSocket error", session_id=session_id, error=str(e))
        await connection_manager.send_message(session_id, {
            "type": "error",
            "message": str(e)
        })
        connection_manager.disconnect(session_id)


async def handle_query_message(session_id: str, message: Dict[str, Any], agent_svc: AgentService):
    """Handle query message from WebSocket"""
    query = message.get("query")
    if not query:
        await connection_manager.send_message(session_id, {
            "type": "error",
            "message": "Query is required"
        })
        return
    
    logger.info("WebSocket query received", session_id=session_id, query=query[:100])
    
    # Send acknowledgment
    await connection_manager.send_message(session_id, {
        "type": "query_received",
        "session_id": session_id,
        "query": query
    })
    
    # Check if streaming is requested
    stream_response = message.get("stream", True)
    
    if stream_response and streaming_handler:
        # Use streaming handler for real-time updates
        await streaming_handler.handle_agent_stream(session_id, agent_svc, query)
    else:
        # Process query normally and send final result
        result = await agent_svc.process_query(query, session_id)
        await connection_manager.send_message(session_id, {
            "type": "query_complete",
            "result": result
        })


# WebSocket Status Endpoint
@app.get("/ws/status")
async def websocket_status():
    """Get WebSocket connection status"""
    return connection_manager.get_connection_stats()


# Error Monitoring Endpoints
@app.get("/monitoring/errors")
async def get_error_metrics():
    """Get error metrics and monitoring data"""
    return error_handler.get_metrics().dict()


@app.get("/monitoring/health")
async def get_detailed_health():
    """Get detailed health check results"""
    return await health_checker.run_health_checks()


@app.post("/monitoring/reset-errors")
async def reset_error_metrics():
    """Reset error metrics (admin endpoint)"""
    error_handler.reset_metrics()
    return {"message": "Error metrics reset successfully"}
