"""
WebSocket Handlers for AP3X Crypto Agent Backend
Real-time streaming support for agent execution and workspace commands
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Set

import structlog
from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel, ValidationError

logger = structlog.get_logger()


class WebSocketMessage(BaseModel):
    """WebSocket message model"""
    type: str
    data: Dict[str, Any]
    timestamp: Optional[str] = None
    session_id: Optional[str] = None


class StreamingEvent(BaseModel):
    """Streaming event model"""
    event_type: str
    event_data: Dict[str, Any]
    timestamp: str
    session_id: str
    step_number: Optional[int] = None


class WorkspaceCommand(BaseModel):
    """Workspace command model"""
    command_type: str
    command_data: Dict[str, Any]
    target: Optional[str] = None
    priority: int = 1


class ConnectionManager:
    """Enhanced WebSocket connection manager with streaming capabilities"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.streaming_sessions: Dict[str, bool] = {}
        self.workspace_subscribers: Set[str] = set()
        
    async def connect(self, websocket: WebSocket, session_id: str, metadata: Optional[Dict[str, Any]] = None):
        """Accept a WebSocket connection with metadata"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        self.connection_metadata[session_id] = metadata or {}
        self.connection_metadata[session_id]["connected_at"] = time.time()
        self.streaming_sessions[session_id] = False
        
        logger.info("WebSocket connected", session_id=session_id, metadata=metadata)
        
        # Send welcome message
        await self.send_message(session_id, {
            "type": "connection_established",
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat(),
            "capabilities": [
                "agent_streaming",
                "workspace_commands",
                "real_time_updates",
                "blockchain_notifications",
                "social_feed_updates"
            ]
        })
    
    def disconnect(self, session_id: str):
        """Remove a WebSocket connection"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
        
        if session_id in self.connection_metadata:
            del self.connection_metadata[session_id]
        
        if session_id in self.streaming_sessions:
            del self.streaming_sessions[session_id]
        
        self.workspace_subscribers.discard(session_id)
        
        logger.info("WebSocket disconnected", session_id=session_id)
    
    async def send_message(self, session_id: str, message: Dict[str, Any]):
        """Send message to a specific WebSocket"""
        if session_id in self.active_connections:
            try:
                # Add timestamp if not present
                if "timestamp" not in message:
                    message["timestamp"] = datetime.utcnow().isoformat()
                
                await self.active_connections[session_id].send_text(json.dumps(message))
                
            except Exception as e:
                logger.error("Failed to send WebSocket message", session_id=session_id, error=str(e))
                self.disconnect(session_id)
    
    async def send_streaming_event(self, session_id: str, event: StreamingEvent):
        """Send a streaming event"""
        await self.send_message(session_id, {
            "type": "streaming_event",
            "event": event.dict()
        })
    
    async def send_workspace_command(self, session_id: str, command: WorkspaceCommand):
        """Send a workspace command"""
        await self.send_message(session_id, {
            "type": "workspace_command",
            "command": command.dict()
        })
    
    async def broadcast_workspace_update(self, update: Dict[str, Any]):
        """Broadcast workspace update to all subscribers"""
        message = {
            "type": "workspace_update",
            "update": update,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        disconnected = []
        for session_id in self.workspace_subscribers:
            if session_id in self.active_connections:
                try:
                    await self.active_connections[session_id].send_text(json.dumps(message))
                except Exception:
                    disconnected.append(session_id)
        
        # Clean up disconnected sessions
        for session_id in disconnected:
            self.disconnect(session_id)
    
    async def start_streaming(self, session_id: str):
        """Start streaming mode for a session"""
        if session_id in self.streaming_sessions:
            self.streaming_sessions[session_id] = True
            await self.send_message(session_id, {
                "type": "streaming_started",
                "session_id": session_id
            })
    
    async def stop_streaming(self, session_id: str):
        """Stop streaming mode for a session"""
        if session_id in self.streaming_sessions:
            self.streaming_sessions[session_id] = False
            await self.send_message(session_id, {
                "type": "streaming_stopped",
                "session_id": session_id
            })
    
    def is_streaming(self, session_id: str) -> bool:
        """Check if session is in streaming mode"""
        return self.streaming_sessions.get(session_id, False)
    
    def subscribe_to_workspace(self, session_id: str):
        """Subscribe session to workspace updates"""
        self.workspace_subscribers.add(session_id)
        logger.info("Session subscribed to workspace updates", session_id=session_id)
    
    def unsubscribe_from_workspace(self, session_id: str):
        """Unsubscribe session from workspace updates"""
        self.workspace_subscribers.discard(session_id)
        logger.info("Session unsubscribed from workspace updates", session_id=session_id)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "streaming_sessions": len([s for s in self.streaming_sessions.values() if s]),
            "workspace_subscribers": len(self.workspace_subscribers),
            "connections": [
                {
                    "session_id": session_id,
                    "connected_at": metadata.get("connected_at"),
                    "is_streaming": self.streaming_sessions.get(session_id, False),
                    "subscribed_to_workspace": session_id in self.workspace_subscribers
                }
                for session_id, metadata in self.connection_metadata.items()
            ]
        }


class StreamingHandler:
    """Handles streaming events from the agent"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.active_streams: Dict[str, asyncio.Task] = {}
    
    async def handle_agent_stream(self, session_id: str, agent_service, query: str):
        """Handle streaming from agent execution"""
        try:
            await self.connection_manager.start_streaming(session_id)
            
            step_number = 0
            async for event in agent_service.stream_query(query, session_id):
                if not self.connection_manager.is_streaming(session_id):
                    break
                
                step_number += 1
                
                streaming_event = StreamingEvent(
                    event_type=event.get("type", "agent_step"),
                    event_data=event.get("data", {}),
                    timestamp=event.get("timestamp", datetime.utcnow().isoformat()),
                    session_id=session_id,
                    step_number=step_number
                )
                
                await self.connection_manager.send_streaming_event(session_id, streaming_event)
                
                # Add small delay to prevent overwhelming the client
                await asyncio.sleep(0.1)
            
            await self.connection_manager.stop_streaming(session_id)
            
        except Exception as e:
            logger.error("Streaming error", session_id=session_id, error=str(e))
            await self.connection_manager.send_message(session_id, {
                "type": "streaming_error",
                "error": str(e)
            })
            await self.connection_manager.stop_streaming(session_id)
    
    async def handle_blockchain_notifications(self, session_id: str, addresses: List[str]):
        """Handle blockchain event notifications"""
        # This would integrate with blockchain event monitoring
        # For now, it's a placeholder for future implementation
        await self.connection_manager.send_message(session_id, {
            "type": "blockchain_monitoring_started",
            "addresses": addresses
        })
    
    async def handle_social_feed_updates(self, session_id: str, keywords: List[str]):
        """Handle real-time social feed updates"""
        # This would integrate with social media streaming APIs
        # For now, it's a placeholder for future implementation
        await self.connection_manager.send_message(session_id, {
            "type": "social_monitoring_started",
            "keywords": keywords
        })


class WorkspaceCommandHandler:
    """Handles workspace commands and updates"""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.command_queue: asyncio.Queue = asyncio.Queue()
        self.command_processor_task: Optional[asyncio.Task] = None
    
    async def start_command_processor(self):
        """Start the command processor task"""
        if self.command_processor_task is None or self.command_processor_task.done():
            self.command_processor_task = asyncio.create_task(self._process_commands())
    
    async def stop_command_processor(self):
        """Stop the command processor task"""
        if self.command_processor_task and not self.command_processor_task.done():
            self.command_processor_task.cancel()
            try:
                await self.command_processor_task
            except asyncio.CancelledError:
                pass
    
    async def queue_command(self, session_id: str, command: WorkspaceCommand):
        """Queue a workspace command"""
        await self.command_queue.put((session_id, command))
    
    async def _process_commands(self):
        """Process queued workspace commands"""
        while True:
            try:
                session_id, command = await self.command_queue.get()
                await self._execute_command(session_id, command)
                self.command_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Command processing error", error=str(e))
    
    async def _execute_command(self, session_id: str, command: WorkspaceCommand):
        """Execute a workspace command"""
        try:
            if command.command_type == "update_portfolio":
                # Handle portfolio update
                await self._handle_portfolio_update(session_id, command.command_data)
            
            elif command.command_type == "alert_price_change":
                # Handle price alert
                await self._handle_price_alert(session_id, command.command_data)
            
            elif command.command_type == "execute_trade":
                # Handle trade execution
                await self._handle_trade_execution(session_id, command.command_data)
            
            elif command.command_type == "update_watchlist":
                # Handle watchlist update
                await self._handle_watchlist_update(session_id, command.command_data)
            
            else:
                logger.warning("Unknown command type", command_type=command.command_type)
            
        except Exception as e:
            logger.error("Command execution failed", 
                        session_id=session_id, 
                        command_type=command.command_type, 
                        error=str(e))
    
    async def _handle_portfolio_update(self, session_id: str, data: Dict[str, Any]):
        """Handle portfolio update command"""
        await self.connection_manager.send_workspace_command(session_id, WorkspaceCommand(
            command_type="portfolio_updated",
            command_data=data
        ))
    
    async def _handle_price_alert(self, session_id: str, data: Dict[str, Any]):
        """Handle price alert command"""
        await self.connection_manager.send_workspace_command(session_id, WorkspaceCommand(
            command_type="price_alert_triggered",
            command_data=data
        ))
    
    async def _handle_trade_execution(self, session_id: str, data: Dict[str, Any]):
        """Handle trade execution command"""
        await self.connection_manager.send_workspace_command(session_id, WorkspaceCommand(
            command_type="trade_executed",
            command_data=data
        ))
    
    async def _handle_watchlist_update(self, session_id: str, data: Dict[str, Any]):
        """Handle watchlist update command"""
        await self.connection_manager.send_workspace_command(session_id, WorkspaceCommand(
            command_type="watchlist_updated",
            command_data=data
        ))
